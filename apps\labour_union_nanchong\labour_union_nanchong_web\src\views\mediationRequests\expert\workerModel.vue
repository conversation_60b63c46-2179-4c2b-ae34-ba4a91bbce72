<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :canFullscreen="false"
  >
    <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'carbon:task-view',
              label: '选择',
              type: 'default',
              onClick: handleSelect.bind(null, record),
              // auth: '/difficultEmployees/choice',
            },
          ]"
        />
      </template>
    </BasicTable>
  </BasicModal>
</template>
<script lang="ts" setup>
import { useModalInner, BasicModal } from '/@/components/Modal'
import { useTable, BasicTable, TableAction } from '/@/components/Table'
import { computed } from 'vue'
import { UserFormSchemas, Usercolumns2 } from './record'
import { useUserStore } from '/@/store/modules/user'
import { getWorkerList } from '/@/api/mediationRequests/legalExperts'
const userStore = useUserStore()

const emit = defineEmits(['success', 'register'])

const title = computed(() => {
  return `人员选择`
})

const [registerModal, {}] = useModalInner(async () => {
  await clearSelectedRowKeys()
})

const [registerTable, { clearSelectedRowKeys }] = useTable({
  rowKey: 'autoId',
  api: getWorkerList,
  columns: Usercolumns2(),
  maxHeight: 435,
  beforeFetch: params => {
    return { ...params }
  },
  formConfig: {
    labelWidth: 120,
    autoSubmitOnEnter: true,
    schemas: UserFormSchemas(),
  },
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/difficultEmployees/choice']
  },
  immediate: true,
  useSearchForm: true,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: true,
})

//选择按钮操作
function handleSelect(record) {
  console.log(record)
  emit('success',   record.userName ,record.phone,record.identityCardNumber, record.gender)
}


</script>

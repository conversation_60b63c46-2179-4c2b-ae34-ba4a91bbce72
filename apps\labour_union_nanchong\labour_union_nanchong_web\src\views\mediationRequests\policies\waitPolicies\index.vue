<template>
  <div>
    <BasicTable @register="registerTable">
      
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'tabler:list-details',
              label: '详情',
              type: 'default',
              onClick: handleDetails.bind(null, record),
              auth: '/policiesAudit/view',
            }, 
            {
              icon: 'ant-design:audit-outlined',
              label: '审核',
              type: 'primary',
              onClick: handleAudit.bind(null, record),
              disabled: record.auditState !== 'wait',
              auth: '/policiesAudit/audit',
            },
          ]"
        />
      </template>
    </BasicTable>

    <RecordModal
      @register="registerRecordModal"
      :canFullscreen="false"
      width="50%"
      @success="handleSuccess"
    />
    <AuditModal @register="registerAuditModal" @success="handleAuditSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, TableAction, useTable } from '/@/components/Table';
import { columns, formSchemas } from './data';
import RecordModal from './recordModal.vue';
import AuditModal  from './auditModal.vue';
import { useModal } from '/@/components/Modal';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  findList,
  saveOrUpdateByDTO,
  getByEntity,
  policiesAudit,
  policyRelease
} from '/@/api/mediationRequests/policies';
import { useMessage } from '@monorepo-yysz/hooks';

//是否详情

const [registerTable, { reload, getSelectRows }] = useTable({
  api: findList,
  beforeFetch: params => {
    params.sourceType = 'worker';
    return params;
  },
  rowKey: 'autoId',
  columns: columns,
  showIndexColumn: true,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/lawyerAudit/view', '/lawyerAudit/edit', '/lawyerAudit/delete', '/lawyerAudit/add'],
    // align: 'left',
    class: '!text-center',
  },
});

const [registerRecordModal, { openModal: openRecordModal, closeModal }] = useModal();
const [registerAuditModal, { openModal: openAuditModal, closeModal: closeAuditModal }] = useModal();
const { createErrorModal, createSuccessModal, createConfirm } = useMessage();

function handleDetails(record) {
  getByEntity({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openRecordModal(true, { record: res?.data, isUpdate: true, disabled: true });
    }
  });
}

//新增律师
function handleAdd() {
  openRecordModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

//编辑操作
function handleEdit(record) {
  getByEntity({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openRecordModal(true, { record: res?.data, isUpdate: true, disabled: false });
    }
  });
}

//发布
function handlePublish(record) {
  const state = record.publisherState === 'wait' || record.publisherState === 'refuse' ? '发布' : '撤销';
  createConfirm({
    iconType: 'warning',
    content: `请确认要${state}${record.policyName}吗？`,
    onOk: function () {
      policyRelease({ autoId: record.autoId, publisherState: record.publisherState === 'wait' || record.publisherState === 'refuse' ? 'pass' : 'refuse' }).then(
        ({ code, message }) => {
          if (code === 200) {
            createSuccessModal({ content: `${state}成功` });
            reload();
          } else {
            createErrorModal({ content: `${state}失败，${message}` });
          }
        }
      );
    },
  });
}


//新增或编辑表单提交后调用
function handleSuccess({ values, isUpdate }) {
  if (isUpdate) {
    console.log(values);

    saveOrUpdateByDTO(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `编辑成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `编辑失败!${message}` });
      }
    });
  } else {
    saveOrUpdateByDTO(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `新增成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `新增失败!${message}` });
      }
    });
  }
}

//审核
function handleAudit(record) {
  openAuditModal(true, {
    isUpload: true,
    record,
  });
}

function handleAuditSuccess(item) {
  console.log(item);
  const { ...params } = item;
  policiesAudit({  ...params }).then(res => {
    if (res?.code === 200) {
      message.success('处理成功');
      reload();
      closeAuditModal();
    }
  });
}
</script>

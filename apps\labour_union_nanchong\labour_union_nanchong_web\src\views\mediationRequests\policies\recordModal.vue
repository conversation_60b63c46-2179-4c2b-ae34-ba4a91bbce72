<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    otherText="确认"
    @other="handleSubmit"
     :showOtherBtn="!disabled"
    @ok="handleOther"
    okText="提取关键字"
   
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>

  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import {BasicForm, useForm} from '/@/components/Form'
import { modalForm, extractKeywordsForPolicy } from './data';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const disabled = ref(false);

const record = ref<Recordable>();

const autoId = ref('');

const userId = ref('');
const userName = ref('');
const companyId = ref('');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.policyName || ''}--详情`
    : unref(isUpdate)
      ? `编辑--${unref(record)?.policyName || ''}--信息`
      : '新增企业管理政策清单';
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  record.value = data.record;

  autoId.value = data.record?.autoId;

  if (unref(isUpdate)) {
    const { otherFilePath, aidApplyFilePath,keywords,attachment } = data.record;
    setFieldsValue({
      ...data.record,
      aidApplyFilePath: aidApplyFilePath ? aidApplyFilePath.split(',') : [],
      otherFilePath: otherFilePath ? otherFilePath.split(',') : [],
      // 设置 keywordOptions 为当前的关键字列表，用于显示所有选项
      keywordOptions: keywords ? keywords.split(',') : [],
      keywords :keywords ? keywords.split(',') : [],
      attachment : attachment ? attachment.split(',') : []
    });
  } else {
    // 新增模式下初始化 keywordOptions 为空数组
    setFieldsValue({
      keywordOptions: [],
      keywords: [],
    });
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

const form = computed(() => {
  return modalForm(unref(disabled), unref(isUpdate));
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const values = await validate();
    values.lawyerCertificate = values.lawyerCertificate?.toString();
    values.resumePath = values.resumePath?.toString();
    values.applyDescription = values.applyDescription?.toString();
    values.keywords = (values.keywords ?? []).join(',');
    values.attachment = (values.attachment ?? []).join(',');
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
        autoId: isUpdate.value ? autoId.value : undefined,
        userId: unref(userId),
        userName: unref(userName),
        companyId: unref(companyId),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}


async function handleOther() {
  const values = getFieldsValue();
  const content = values.content;
  const newKeywords = await extractKeywordsForPolicy(content);

  if (newKeywords.length === 0) {
    return; // 如果没有提取到关键字，直接返回
  }
  // 同时设置两个字段
  setFieldsValue({
    keywordOptions: newKeywords,
    keywords: newKeywords,
  });
}
</script>

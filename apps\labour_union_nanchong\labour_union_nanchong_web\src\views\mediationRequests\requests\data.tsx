import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '/@/api/sys/upload'
import {useUserStore} from "/@/store/modules/user";
const dictionary = useDictionary()
const userStore = useUserStore()


export const columns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    defaultHidden: true,
  },
  {
    title: '职工姓名',
    dataIndex: 'userName',
    width: 100,
  },
  {
    title: '诉求标题',
    dataIndex: 'reqTitle',
    width: 100,
  },
  {
    title: '诉求类型',
    dataIndex: 'reqType',
    width: 100,
    customRender: ({ text }) => {
      return dictionary.getDictionaryMap.get(`requestType_${text}`)?.dictName
    },
  },

  {
    title: '审核状态',
    dataIndex: 'auditState',
    width: 100,
    customRender: ({ text }) => {
      return (
        <span
          class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
        >{dictionary.getDictionaryMap.get(`groupStudyStatus_${text}`)?.dictName}</span>
      );
    },
  },
  {
    title: '提交时间',
    dataIndex: 'createTime',
    width: 100,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
]

export const formSchemas: FormSchema[] = [

  {
    field: 'userName',
    component: 'Input',
    label: '职工姓名',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入职工姓名',
    },
  },

  {
    field: 'reqTitle',
    component: 'Input',
    label: '诉求标题',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入诉求标题',
    },
  },
  {
    field: 'reqType',
    component: 'Select',
    label: '诉求类型',
    colProps: { span: 6 },
    itemProps: {
      autoLink: true,
    },
    componentProps: function () {
      return {
        fieldNames: { label: 'dictName', value: 'dictCode' },
        options: dictionary.getDictionaryOBJMap.get('requestType'),
      }
    },
  },
]

export const modalForm = (disabled: boolean, isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'userName',
      component: 'Input',
      label: '职工名称',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'reqType',
      component: 'Select',
      label: '诉求类型',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps: function () {
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: dictionary.getDictionaryOBJMap.get('requestType'),
        }
      },
    },
    {
      field: 'reqTitle',
      component: 'Input',
      label: '诉求标题',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'reqContent',
      component: 'Input',
      label: '诉求内容',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'attachment',
      label: '材料文件',
      colProps: { span: 12 },
      component: 'Upload',
      // required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 134,
        },
        maxNumber: 1,
        maxSize: 10,
      },
    },
    {
      field: 'keywords',
      label: '关键词',
      colProps: { span: 12 },
      component: 'CheckboxGroup',
      componentProps: ({ formModel }) => {
        // 只用 keywordOptions，不再回退 keywords
        const keywordOptions = formModel.keywords || [];
        return {
          options: keywordOptions.map(item => ({
            label: item,
            value: item,
          })),
        }
      }
    },
    {
      field: 'auditState',
      label: '审核状态',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      ifShow: ({ values }) => !!values.auditState,
      componentProps:{
        options: dictionary.getDictionaryOpt.get('groupStudyStatus'),
      }
    },
    {
      field: 'auditRemark',
      label: '审核备注',
      required: true,
      component: 'Input',
      colProps: { span: 12 },
      ifShow: ({ values }) => !!values.auditRemark,
    },
    {
      field: 'policyName',
      label: '匹配政策',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      ifShow: ({ values }) => values.auditState == 'pass',
    },
  ]
}

export const auditModalForm: FormSchema[] = [
  {
    field: 'auditState',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    defaultValue: 'pass',
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'pass',
        },
        {
          label: '拒绝',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'auditRemark',
    label: '审核意见',
    required: function (Recordable) {
      if (Recordable.model.auditState == 'refuse') {
        return true
      } else {
        return false
      }
    },
    component: 'InputTextArea',
  },
]
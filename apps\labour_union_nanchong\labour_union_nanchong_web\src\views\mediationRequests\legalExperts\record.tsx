import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '/@/api/sys/upload'
import { validateIdNum, validatePhone } from '@monorepo-yysz/utils';
import { getUserListHasCompanyId } from '/@/api/educationAid/hardWorkerAudit'
// import { findVoList } from '/@/api/consultation/lawyersType'
import { nextTick } from 'vue'
import {useUserStore} from "/@/store/modules/user";
import {searchNextUnionForm} from "/@/utils/searchNextUnion";

const dictionary = useDictionary()
const userStore = useUserStore()


export const columns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    defaultHidden: true,
  },
  {
    title: '职工编号',
    dataIndex: 'userId',
    width: 100,
    defaultHidden: true,
  },
  {
    title: '姓名',
    dataIndex: 'lawyerName',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName}</span>
    },
  },

  {
    title: '律师类型',
    dataIndex: 'lawyerType',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`lawyersType_${text}`)?.dictName}</span>
    },
  },

  {
    title: '联系电话',
    dataIndex: 'telephone',
    width: 100,
  },

  {
    title: '所属地区',
    dataIndex: 'localArea',
    width: 100,
  },

  {
    title: '入驻时间',
    dataIndex: 'createTime',
    width: 100,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },

]

export const formSchemas: FormSchema[] = [

  {
    field: 'lawyerName',
    component: 'Input',
    label: '律师姓名',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入律师姓名',
    },
  },

  {
    field: 'typeName',
    component: 'Select',
    label: '律师类型',
    colProps: { span: 6 },
    itemProps: {
      autoLink: true,
    },
    ifShow({ disabled, values }) {
      return !disabled && !values.userName
    },
    componentProps: function () {
      return {
        fieldNames: { label: 'dictName', value: 'dictName' },
        options: dictionary.getDictionaryOBJMap.get('lawyersType'),
      }
    },
   
  },

  {
    field: 'localArea',
    label: '所属地区',
    component: 'Select',
    colProps: { span: 5 },
    rulesMessageJoinLabel: true,
    ifShow: userStore.getUserInfo.companyId === '1eb05ea222a54b4c8b5cc6fe4d989ce0',
    componentProps: function () {
      return {
        fieldNames: { label: 'dictName', value: 'dictName' },
        options: dictionary.getDictionaryOBJMap.get('quxian'),
      }
    },
  },

]

export const modalForm = (  disabled: boolean, isUpdate: boolean,): FormSchema[] =>{
  return[
    // {
    //   field: 'userId',
    //   label: '工会用户',
    //   required: true,
    //   itemProps: {
    //     autoLink: true,
    //   },
    //   ifShow({ disabled, values }) {
    //     return !disabled && !values.lawyerName
    //   },
    //   component: 'ApiSelect',
    //   colProps: { span: 12 },
    //   componentProps: ({}) => {
    //     return {
    //       api: getUserListHasCompanyId,
    //       params: {
    //         pageSize: 0,
    //         source: 'lawyer',
    //         // companyId: userStore.getUserInfo.companyId,
    //       },
    //       resultField: 'data',
    //       immediate: true,
    //       alwaysLoad: true,
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.nickname.toLowerCase().indexOf(input.toLowerCase()) >= 0
    //       },
    //       fieldNames: { label: 'nickname', value: 'userId' },
    //       placeholder: '请选择工会用户',
    //     }
    //   },
    // },

    // {
    //   field: 'lawyerName',
    //   component: 'ShowSpan',
    //   label: '姓名',
    //   colProps: {
    //     span: 12,
    //   },
    //   required: true,
    //   dynamicDisabled: true,
    //   ifShow({ values }) {
    //     return values.lawyerName
    //   },
    // },

    {
      field: 'lawyerName',
      component: 'Input',
      label: '姓名',
      colProps: {
        span: 12,
      },
      // slot: 'button',
      required: true,
      ifShow: !disabled,
    },

    {
      field: 'lawyerName',
      component: 'Input',
      label: '姓名',
      colProps: {
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: disabled,
    },



    {
      field: 'sex',
      label: '性别',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`gender`),
      },
    },
    {
      field: 'idCard',
      label: '职工身份证号',
      colProps: { span: 12 },
      component: 'Input',
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
    },
    {
      field: 'telephone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'Input',
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
    },

    {
      field: 'localArea',
      label: '所在地区',
      colProps: { span: 12 },
      component: 'Input',
      // required: true,
      componentProps: {
        autocomplete: 'off',
        placeholder: '请输入所在地区,不能超过50个字符',
        showCount: true,
        maxlength: 50,
      },
    },
    {
      field: 'lawyerType',
      component: 'Select',
      label: '律师类型',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      componentProps: function () {
        return {
          fieldNames: { label: 'dictName', value: 'dictCode' },
          options: dictionary.getDictionaryOBJMap.get('lawyersType'),
        }
      },
    },

    {
      field: 'lawyerCertificate',
      label: '律师资格证',
      colProps: { span: 12 },
      component: 'Upload',
      // required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 130,
        },
        maxNumber: 1,
        maxSize: 10,
      },
    },

    // {
    //   field: 'resumePath',
    //   label: '个人简历',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 130,
    //     },
    //     maxNumber: 1,
    //     maxSize: 10,
    //   },
    // },

    // {
    //   field: 'applyDescription',
    //   label: '申请书',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 130,
    //     },
    //     maxNumber: 1,
    //     maxSize: 10,
    //   },
    // },

    // {
    //   field: 'createTime',
    //   label: '入驻时间',
    //   colProps: { span: 12 },
    //   component: 'Input',
    // },

    // {
    //   field: 'auditStatus',
    //   label: '审核状态',
    //   required: true,
    //   component: 'Select',
    //   colProps: { span: 12 },
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`applyState`),
    //     placeholder: '请选择课程来源',
    //   },
    // },
  ]
}

export const auditModalForm: FormSchema[] = [
  {
    field: 'operateType',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'pass',
        },
        {
          label: '拒绝',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'remark',
    label: '审核意见',
    required: function (Recordable) {
      if (Recordable.model.auditstatus == 'refuse') {
        return true
      } else {
        return false
      }
    },
    component: 'InputTextArea',
  },
]


//选择用户弹框筛选条件
export const UserFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//筛选用户列表
export const Usercolumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },

    {
      title: '电话',
      dataIndex: 'phone',
    },
  ]
}

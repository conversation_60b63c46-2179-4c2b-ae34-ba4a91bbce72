import { h5Http } from '@/utils/http/axios';
import { BasicResponse } from '@monorepo-yysz/types';

enum InterestGroup {
  base = "/industrialReformInfoPengAn",
  //分页查询
  findList = '/findVoList',
  //新增规则
  saveOrUpdate = '/saveOrUpdateByDTO',
  getVoByDto = '/getVoByDto',
  simpleList = '/simpleList'
}

function getApi(url?: string) {
  if (!url) {
    return InterestGroup.base;
  }
  return InterestGroup.base + url;
}

//列表
export const list = params => {
  return h5Http.get<BasicResponse>(
    { url: getApi(InterestGroup.findList), params },
    {
      isTransformResponse: false,
    }
  );
};

//新增修改
export const saveOrUpdate = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(InterestGroup.saveOrUpdate),
      params,
    },
    {
      isTransformResponse: false,
    }
  );
};


export const getByGroupId = params =>{
  return h5Http.get<BasicResponse>(
      { url: getApi(InterestGroup.getVoByDto), params },
  );
}

export const deleteByGroupId = groupId =>{
  return h5Http.delete<BasicResponse>(
      { url: `${getApi()}?groupId=${groupId}` },
      {
        isTransformResponse: false,
      }
  );
}

//列表 只查询groupId groupName
export const simpleList = params => {
    return h5Http.get<BasicResponse>(
        { url: getApi(InterestGroup.simpleList), params },
        {
            isTransformResponse: false,
        }
    );
};

import { BasicColumn, FormSchema } from '/@/components/Table';
import { useDictionary } from '/@/store/modules/dictionary';
import { uploadApi } from '/@/api/sys/upload';
import { validateIdNum, validatePhone } from '@monorepo-yysz/utils';
import { getUserListHasCompanyId } from '/@/api/educationAid/hardWorkerAudit';
// import { findVoList } from '/@/api/consultation/lawyersType'
import { nextTick } from 'vue';
import { useUserStore } from '/@/store/modules/user';
import { searchNextUnionForm } from '/@/utils/searchNextUnion';
import { map } from 'lodash-es';
import {Tag} from "ant-design-vue";
const dictionary = useDictionary();
const userStore = useUserStore();

export const columns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    defaultHidden: true,
  },
  {
    title: '专家姓名',
    dataIndex: 'name',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName}</span>;
    },
  },
  {
    title: '擅长领域',
    dataIndex: 'domainType',
    width: 100,
    customRender({text}){
      const typeList = text.split(',');
      console.log(typeList);
      if (typeList.length > 0) {
        return (
          <div>
            {map(typeList || [], t => (
              <div className={`inline-block p-1`}>
                <Tag color={'blue'}>{dictionary.getDictionaryMap.get(`expertiseArea_${t}`)?.dictName}</Tag>
              </div>
            ))}
          </div>
        );
      }
    },
  },
  {
    title: '专家类型',
    dataIndex: 'expertType',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`expertType_${text}`)?.dictName}</span>;
    },
  },

  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 100,
  },

  {
    title: '状态',
    dataIndex: 'isAvailable',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`enableOrDisable_${text}`)?.dictName}</span>;
    },
  },

  {
    title: '入驻时间',
    dataIndex: 'createTime',
    width: 100,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
];

export const formSchemas: FormSchema[] = [
  {
    field: 'expertType',
    component: 'Select',
    label: '专家类型',
    colProps: {
      span: 5,
    },
    componentProps:{
      options: dictionary.getDictionaryOpt.get('expertType'),
    }
  },
  {
    field: 'name',
    component: 'Input',
    label: '专家姓名',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入专家姓名',
    },
  },

  {
    field: 'phone',
    component: 'Input',
    label: '联系方式',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入联系方式',
    },
  },

  {
    field: 'domainType',
    component: 'Select',
    label: '擅长领域',
    colProps: { span: 6 },
    itemProps: {
      autoLink: true,
    },
    ifShow({ disabled, values }) {
      return !disabled && !values.userName;
    },
    componentProps: function () {
      return {
        fieldNames: { label: 'dictName', value: 'dictCode' },
        options: dictionary.getDictionaryOBJMap.get('expertiseArea'),
      };
    },
  },
];

export const modalForm = (disabled: boolean, isUpdate: boolean): FormSchema[] => {
  return [
    // {
    //   field: 'userId',
    //   label: '工会用户',
    //   required: true,
    //   itemProps: {
    //     autoLink: true,
    //   },
    //   ifShow({ disabled, values }) {
    //     return !disabled && !values.lawyerName
    //   },
    //   component: 'ApiSelect',
    //   colProps: { span: 12 },
    //   componentProps: ({}) => {
    //     return {
    //       api: getUserListHasCompanyId,
    //       params: {
    //         pageSize: 0,
    //         source: 'lawyer',
    //         // companyId: userStore.getUserInfo.companyId,
    //       },
    //       resultField: 'data',
    //       immediate: true,
    //       alwaysLoad: true,
    //       showSearch: true,
    //       filterOption: (input: string, option: any) => {
    //         return option.nickname.toLowerCase().indexOf(input.toLowerCase()) >= 0
    //       },
    //       fieldNames: { label: 'nickname', value: 'userId' },
    //       placeholder: '请选择工会用户',
    //     }
    //   },
    // },

    // {
    //   field: 'lawyerName',
    //   component: 'ShowSpan',
    //   label: '专家姓名',
    //   colProps: {
    //     span: 12,
    //   },
    //   required: true,
    //   dynamicDisabled: true,
    //   ifShow({ values }) {
    //     return values.lawyerName
    //   },
    // },
    {
      field: 'expertType',
      component: 'Select',
      label: '专家类型',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      // componentProps: function () {
      //   return {
      //     fieldNames: { label: 'dictName', value: 'dictCode' },
      //     options: dictionary.getDictionaryOBJMap.get('expertType'),
      //   }
      // },
      componentProps: ({ formModel }) => {
        return {
          options: dictionary.getDictionaryOpt.get('expertType'),
          onChange: () => {
            formModel.name = '';
            formModel.idCard = '';
            formModel.deptName = '';
            formModel.phone = '';
            formModel.sex = ''
          },
        };
      },
    },
    // 专家类型为法律专家
    {
      field: 'name',
      component: 'Input',
      label: '专家姓名',
      colProps: {
        span: 12,
      },
      slot: 'button',
      required: true,
      ifShow: ({ values }) => !disabled && values.expertType == 'law',
    },
    // 专家类型为工会干部时
    {
      field: 'name',
      component: 'Input',
      label: '专家姓名',
      colProps: {
        span: 12,
      },
      slot: 'button1',
      required: true,
      ifShow: ({ values }) => !disabled && values.expertType == 'cadre',
    },
    // 专家类型为劳模工匠
    {
      field: 'name',
      component: 'Input',
      label: '专家姓名',
      colProps: {
        span: 12,
      },
      slot: 'button2',
      required: true,
      ifShow: ({ values }) => !disabled && values.expertType == 'worker',
    },
    {
      field: 'name',
      component: 'Input',
      label: '专家姓名',
      colProps: {
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: ({ values }) => !disabled && values.expertType == null ,
      componentProps: {
        placeholder: '请优先选择专家类型',
      }
    },
    {
      field: 'name',
      component: 'Input',
      label: '专家姓名',
      colProps: {       
        span: 12,
      },
      required: true,
      dynamicDisabled: true,
      ifShow: disabled,
    },

    {
      field: 'sex',
      label: '性别',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get(`gender`),
      },
    },
    {
      field: 'idCard',
      label: '职工身份证号',
      colProps: { span: 12 },
      component: 'Input',
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
    },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'Input',
      rules: [{ required: true, validator: validatePhone, trigger: ['change', 'blur'] }],
    },
    {
      field: 'domainType',
      component: 'Select',
      label: '专业领域',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
      rules: [
        {
          required: true,
          trigger: ['blur', 'change'],
          validator: async (_, value) => {
            if (value?.length > 3) {
              return Promise.reject('最多添加3个领域');
            }
            return Promise.resolve();
          },
        },
      ],
      componentProps: {
        mode: 'multiple',
        placeholder: '请选择擅长领域（最多3个）',
        options: map(dictionary.getDictionaryOpt.get('expertiseArea'),t => ({
          value: t.value,
          label: t.label,
        })),
      },
      // componentProps: function () {
      //   return {
      //     fieldNames: { label: 'dictName', value: 'dictCode' },
      //     options: dictionary.getDictionaryOBJMap.get('expertiseArea'),
      //   }
      // },
    },

    {
      field: 'avatars',
      label: '专家头像',
      colProps: { span: 12 },
      component: 'Upload',
      required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 131,
        },
        maxNumber: 1,
        maxSize: 10,
      },
    },

    {
      field: 'profile',
      label: '专家简介',
      colProps: { span: 12 },
      component: 'InputTextArea',
      componentProps: {
        rows: 3,
        maxlength: 200,
        showCount: true,
        placeholder: '请输入专家简介内容',
      },
    },

    // {
    //   field: 'applyDescription',
    //   label: '申请书',
    //   colProps: { span: 12 },
    //   component: 'Upload',
    //   componentProps: {
    //     api: uploadApi,
    //     uploadParams: {
    //       operateType: 130,
    //     },
    //     maxNumber: 1,
    //     maxSize: 10,
    //   },
    // },

    // {
    //   field: 'createTime',
    //   label: '入驻时间',
    //   colProps: { span: 12 },
    //   component: 'Input',
    // },

    // {
    //   field: 'auditStatus',
    //   label: '审核状态',
    //   required: true,
    //   component: 'Select',
    //   colProps: { span: 12 },
    //   componentProps: {
    //     options: dictionary.getDictionaryOpt.get(`applyState`),
    //     placeholder: '请选择课程来源',
    //   },
    // },
  ];
};

export const auditModalForm: FormSchema[] = [
  {
    field: 'operateType',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'pass',
        },
        {
          label: '拒绝',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'remark',
    label: '审核意见',
    required: function (Recordable) {
      if (Recordable.model.auditstatus == 'refuse') {
        return true;
      } else {
        return false;
      }
    },
    component: 'InputTextArea',
  },
];

//选择用户弹框筛选条件
export const UserFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'lawyerName',
      label: '律师名称',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};

//选择干部条件
export const cadreFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'tel',
      label: '干部手机号',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ];
};
//筛选用户列表
export const Usercolumns = (): BasicColumn[] => {
  return [
    {
      title: '律师姓名',
      dataIndex: 'lawyerName',
    },
    {
      title: '性别',
      dataIndex: 'sex',
      customRender: ({ text }) => {
        return dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName;
      },
    },
    {
      title: '联系方式',
      dataIndex: 'telephone',
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
    },
  ];
};

//筛选用户列表
export const Usercolumns1 = (): BasicColumn[] => {
  return [
    {
      title: '干部姓名',
      dataIndex: 'cadreName',
    },
    // {
    //   title: '部门名称',
    //   dataIndex: 'deptName',
    // },
    {
      title: '联系方式',
      dataIndex: 'contractPhone',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      customRender: ({ text }) => {
        return text == 1 ? '男' : '女';
      },
    },
    {
      title: '身份证号',
      dataIndex: 'cadreIdentity',
      defaultHidden: true,
    },
  ];
};


export const Usercolumns2 = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'userName',
    },
    {
      title: '类型',
      dataIndex: 'modelType',
      customRender: ({ text }) => {
        return dictionary.getDictionaryMap.get(`workerType_${text}`)?.dictName;
      },
    },
    // {
    //   title: '部门名称',
    //   dataIndex: 'deptName',
    // },
    {
      title: '联系方式',
      dataIndex: 'phone',
    },
    {
      title: '性别',
      dataIndex: 'gender',
      customRender: ({ text }) => {
        return dictionary.getDictionaryMap.get(`gender_${text}`)?.dictName;
      },
    },
    {
      title: '身份证号',
      dataIndex: 'cadreIdentity',
      defaultHidden: true,
    },
  ];
};

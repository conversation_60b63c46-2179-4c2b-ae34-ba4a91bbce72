<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    :show-ok-btn="false"
    :wrap-class-name="$style['reply']"
  >
    <div id="chat-box">
      <a-list
        :pagination="recordData && recordData.length ? pagination : null"
        :data-source="recordData"
      >
        <template #renderItem="{ item }">
          <a-list-item :key="item.id">
            <a-comment :author="item.userName">
              <template #content>
                <div>
                  <div>{{ item.content }}</div>
                  <div class="flex flex-wrap">
                    <template
                      v-for="image in item.imageList"
                      v-if="item.imageList && item.imageList.length"
                    >
                      <a-image
                        :src="image"
                        class="!w-[70px] mr-10px"
                      ></a-image>
                    </template>
                  </div>
                  <div class="w-full flex justify-end">
                    <div class="flex justify-center items-center mr-15px">
                      <Icon
                        icon="hugeicons:view"
                        :size="18"
                      />
                      <div class="ml-5px">{{ item.readCount }}</div>
                    </div>
                    <div class="flex justify-center items-center mr-15px">
                      <Icon
                        icon="iconamoon:like"
                        :size="18"
                      />
                      <div class="ml-5px">{{ item.likeCount }}</div>
                    </div>
                    <a
                      class="flex justify-center items-center"
                      @click="getSubList(item)"
                    >
                      <Icon
                        icon="lsicon:comments-outline"
                        :size="18"
                      />
                      <div class="ml-5px">{{ item.replyCount }}</div>
                    </a>
                  </div>
                  <div
                    class="sub-reply"
                    v-if="item.showReply"
                  >
                    <a-list
                      item-layout="horizontal"
                      :data-source="item.replies"
                    >
                      <template #renderItem="{ item: sub }">
                        <a-list-item :key="sub.autoId">
                          <a-comment :author="sub.userName">
                            <template #content>
                              <div class="!w-full">
                                <div>{{ sub.content }}</div>
                              </div>
                            </template>
                            <template #datetime>
                              <a-tooltip :title="sub.createTime">
                                <span>{{ sub.createTime }}</span>
                              </a-tooltip>
                            </template>
                          </a-comment>
                        </a-list-item>
                      </template>
                    </a-list>
                    <!--                    <div class="w-full flex justify-center" @click="item.showReply = false">-->
                    <!--                      <a>收起 <Icon icon="ant-design:up-outlined" /></a>-->
                    <!--                    </div>-->
                  </div>
                </div>
              </template>
              <template #datetime>
                <a-tooltip :title="item.createTime">
                  <span>{{ item.createTime }}</span>
                </a-tooltip>
              </template>
            </a-comment>
          </a-list-item>
        </template>
      </a-list>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useUserStore } from '/@/store/modules/user';
import { Icon } from '@monorepo-yysz/ui';
import { list } from '@/api/interestGroupManage/comments';
import { startsWith } from 'lodash-es';
const userStore = useUserStore();
const isUpdate = ref(true);

const recordData = ref([]);
const record = ref<Recordable>({});
const title = computed(() => {
  return `${unref(record)?.groupName} - 小组动态`;
});

const pagination = ref({
  pageSize: 5,
  current: 1,
  total: 0,
  showTotal: (total, range) => {
    return ' 共' + total + '条数据';
  },
  showSizeChanger: true, //是否显示更改每页条数
  showQuickJumper: true, //是否显示跳至第几页
  pageSizeOptions: ['5', '10', '20', '40', '80', '100'],
  hideOnSinglePage: false, // 只有一页时是否隐藏分页器
  position: 'bottom', //指定分页显示的位置 'top' | 'bottom' | 'both'
  // 设置页面变化时的回调，调用methods中的onChange方法
  onChange: e => {
    pagination.value.current = e;
    getList();
  },

  // 设置每页显示条数变化时的回调
  onShowSizeChange: (page, pageSize) => {
    pagination.value.current = page;

    pagination.value.pageSize = pageSize;

    getList(pagination.value.current, pagination.value.pageSize);
  },
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  record.value = data.record;
  setModalProps({
    confirmLoading: false,
  });
  isUpdate.value = !!data?.isUpdate;
  await nextTick();
  getList();
});
function getList() {
  list({
    commentType: 'dynamic',
    groupId: unref(record).groupId,
    pageNum: pagination.value.current,
    pageSize: pagination.value.pageSize,
  }).then(res => {
    if (res.code === 200) {
      pagination.value.total = res.total;

      recordData.value = res.data?.map(t => {
        const { images } = t;
        t.imageList =
          images?.split(',')?.map(img => {
            return startsWith(img, 'http') ? img : userStore.getPrefix + img;
          }) ?? [];
        return t;
      });
    }
  });
}

function getSubList(item) {
  if (item.replyCount === 0) {
    return;
  }
  if (item.showReply) {
    item.showReply = false;
    return;
  }
  list({
    pid: item.commentsId,
    groupId: unref(record).groupId,
    pageSize: 0,
  }).then(res => {
    if (res.code === 200) {
      item.replies = res.data;
      item.showReply = true;
    }
  });
}
</script>

<style lang="less" module>
.reply {
  :global {
    .ant-spin-container {
    }
    .ant-list-item {
      display: block;
      .ant-comment-inner {
        padding: 0;
      }
    }
  }
}
</style>

<template>
  <div>
    <BasicTable @register="registerTable">
      <!-- <template #toolbar> -->
        <!-- <a-button
          type="primary"
          @click="handleAdd"
        >
          新增律师
        </a-button> -->
        <!-- <a-button type="primary" @click="handleAdd" auth="/lawyerAudit/add"> 新增律师 </a-button> -->
      <!-- </template> -->
      <template #action="{ record }">
        <TableAction
          :actions="[
            {
              icon: 'tabler:list-details',
              label: '详情',
              type: 'default',
              onClick: handleDetails.bind(null, record),
              auth: '/mediation/view',
            },

            // {
            //   icon: 'fa6-solid:pen-to-square',
            //   label: '编辑',
            //   type: 'primary',
            //   // ifShow: record.isCanOperate,
            //   onClick: handleEdit.bind(null, record),
            //   // auth: '/lawyerAudit/edit',
            // },

            // {
            //   icon: 'fluent:delete-20-filled',
            //   label: '删除',
            //   type: 'primary',
            //   danger: true,
            //   // ifShow: record.isCanOperate,
            //   onClick: handleDelete.bind(null, record),
            //   // auth: '/lawyerAudit/delete',
            // },

            // {
            //   icon: 'ant-design:audit-outlined',
            //   label: '审核',
            //   type: 'primary',
            //   onClick: handleAudit.bind(null, record),
            //   disabled: record.auditStatus !== 'wait',
            //   auth: '/lawyerAudit/audit',
            // },
          ]"
        />
      </template>
    </BasicTable>
    <RecordModal
      @register="registerRecordModal"
      :canFullscreen="false"
      width="50%"
      @success="handleSuccess"
    />
  </div>
</template>

<script lang="ts" setup>
import { createVNode } from 'vue';
import { BasicTable, TableAction, useTable } from '/@/components/Table';
import { columns, formSchemas } from './data';
import RecordModal from './recordModal.vue';
import { useModal } from '/@/components/Modal';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import {
  findList,
  saveOrUpdateByDTO,
  getByEntity,
  logicDelById,
} from '/@/api/mediationRequests/mediation';
import { useMessage } from '@monorepo-yysz/hooks';

//是否详情

const [registerTable, { reload, getSelectRows }] = useTable({
  api: findList,
  beforeFetch: params => {
    return params;
  },
  rowKey: 'autoId',
  columns: columns,
  showIndexColumn: true,
  formConfig: {
    labelWidth: 120,
    schemas: formSchemas,
    autoSubmitOnEnter: true,
    actionColOptions: { span: 3 },
  },
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 200,
    dataIndex: 'action',
    slots: { customRender: 'action' },
    fixed: undefined,
    // auth: ['/lawyerAudit/view', '/lawyerAudit/edit', '/lawyerAudit/delete', '/lawyerAudit/add'],
    // align: 'left',
    class: '!text-center',
  },
});

const [registerRecordModal, { openModal: openRecordModal, closeModal }] = useModal();
const { createSuccessModal, createErrorModal } = useMessage();

function handleDetails(record) {
  getByEntity({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openRecordModal(true, { record: res?.data, isUpdate: true, disabled: true });
    }
  });
}

//新增律师
function handleAdd() {
  openRecordModal(true, {
    isUpdate: false,
    disabled: false,
  });
}

//编辑操作
function handleEdit(record) {
  getByEntity({ autoId: record.autoId }).then(res => {
    if (res.code === 200) {
      openRecordModal(true, { record: res?.data, isUpdate: true, disabled: false });
    }
  });
}

function handleAudit(record) {
  openAuditModal(true, {
    isUpload: true,
    record,
  });
}

function handleAuditSuccess(item) {
  console.log(item);
  const { autoId, ...params } = item;
  lawyerAudit({ todoValueList: [item.autoId], ...params }).then(res => {
    if (res?.code === 200) {
      message.success('处理成功');
      reload();
    }
  });
}
//新增或编辑表单提交后调用
function handleSuccess({ values, isUpdate }) {
  if (isUpdate) {
    console.log(values);

    saveOrUpdateByDTO(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `编辑成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `编辑失败!${message}` });
      }
    });
  } else {
    saveOrUpdateByDTO(values).then(res => {
      const { code, message } = res;
      if (code === 200) {
        createSuccessModal({ content: `新增成功` });
        reload();
        closeModal();
      } else {
        createErrorModal({ content: `新增失败!${message}` });
      }
    });
  }
}

//逻辑删除
function handleDelete(record) {
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除律师--` + '"' + record.lawyerName + '"' + '?',
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise<void>(resolve => {
          logicDelById(record.autoId).then(res => {
            if (res.code === 200) {
              message.success('删除成功');
            } else {
              message.error('删除失败');
            }
            reload();
            resolve();
          });
        });
      } catch {
        return console.log('Oops errors!');
      }
    },
  });
}
</script>

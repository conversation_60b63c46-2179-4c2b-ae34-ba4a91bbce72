<template>
  <BasicModal
      @register="registerModal"
      v-bind="$attrs"
      :title="title"
      @ok="handleSubmit"
  >
    <BasicForm @register="registerForm"  :class="disabledClass"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { modalForm} from "@/views/industrialGroup/groupInfo/data";

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();

const isUpdate = ref(false);
const disabled = ref(false)

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : '';
});

const formItem = computed(() => {
  return modalForm(unref(isUpdate));
});

const title = computed(() => {
  if(unref(disabled)){
    return `${unref(record)?.groupName} - 详情`
  }
  return unref(isUpdate) ? `编辑 - ${unref(record)?.groupName || ''}` : '新增兴趣小组';
});

const [registerForm, { resetFields, validate,setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  record.value = data.record;
  disabled.value = !!data.disabled
  isUpdate.value = !!data.isUpdate;

  if (unref(isUpdate)) {
    await setFieldsValue({...data.record});
  }
  setProps({ disabled: unref(disabled) });
  setModalProps({ confirmLoading: false, showOkBtn: !unref(disabled) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
        ...unref(record),
        ...values,
      },
      isUpdate: unref(isUpdate),
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

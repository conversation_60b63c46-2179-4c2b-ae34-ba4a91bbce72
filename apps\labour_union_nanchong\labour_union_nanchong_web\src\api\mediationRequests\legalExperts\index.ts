import { h5Http } from '/@/utils/http/axios'

import { BasicResponse } from '@monorepo-yysz/types';

enum recordUrl {
  findList = '/findVoList',
  delete = '/record/delete',
  saveOrUpdate = '/record/saveOrUpdate',
  audit = '/applyAudit',
  details = '/record/getDetails',
  saveOrUpdateByDTO = '/saveOrUpdateByDTO',
  getByEntity = '/getByEntity',
  logicDelById = '/logicDelById',
  //劳模信息
  worker = '/modelCraftsmanInfoPengAn/findModelCraftsmanInfoPengAnList'
}

function getApi(url?: string) {
  if (!url) {
    return '/consultationLawyerPengAn'
  }
  return '/consultationLawyerPengAn' + url
}

/*律师列表管理*/
export const getLawyerList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.findList),
      params,
    },
    { isTransformResponse: false },
  )
}

/*律师详情*/
export const getLawyerDetail = params => {
    return h5Http.get<BasicResponse>(
        {
            url: getApi(recordUrl.getByEntity),
            params,
        },
        { isTransformResponse: false },
    )
}

/*新增/编辑管理*/
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.saveOrUpdateByDTO),
      params,
    },
    { isTransformResponse: false },
  )
}
/*批量审核*/
export const lawyerAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.audit),
      params,
    },
    { isTransformResponse: false },
  )
}

/*删除管理*/
export const logicDelById = params => {
  return h5Http.delete<BasicResponse>(
    {url: getApi(recordUrl.logicDelById)+`?autoId=${params}`,
    params,
  },
    
    { isTransformResponse: false },
  )
}

/*劳模信息*/
export const getWorkerList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: recordUrl.worker,
      params,
    },
    { isTransformResponse: false },
  )
}

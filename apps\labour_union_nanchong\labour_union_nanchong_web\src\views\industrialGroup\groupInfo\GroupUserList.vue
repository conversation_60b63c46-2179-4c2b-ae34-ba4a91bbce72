<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" :show-ok-btn="false">
    <BasicTable @register="registerTable" @edit-change="handleEnd">
      <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'action'">
        <TableAction :actions="createActions(record, column)" />
      </template>
      </template>
    </BasicTable>
    <GroupUserModal @register="registerDetailsModal"
                    :canFullscreen="false"
                    width="50%"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { createVNode, ref, unref } from 'vue'
import {
  BasicTable,
  useTable,
  TableAction,
  BasicColumn,
  ActionItem,
  EditRecordRow,
} from '/@/components/Table'
import { BasicModal, useModal, useModalInner } from '/@/components/Modal'
import {userColumns, userFormSchemas,} from './data'
import { message, Modal } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  CloseCircleFilled,
  ExclamationCircleOutlined,
} from '@ant-design/icons-vue'
import {deleteLine, groupUserList, updateState} from "@/api/industrialGroup/groupUser";
import GroupUserModal from "@/views/industrialGroup/groupUser/GroupUserModal.vue";

defineEmits(['success', 'register'])

const groupId = ref('')

const title = ref('成员管理')
const currentEditKeyRef = ref('')

const [registerDetailsModal, { openModal: openDetailsModel }] = useModal()

const [registerTable, { reload }] = useTable({
  rowKey: 'autoId',
  columns: userColumns,
  showIndexColumn: false,
  api: groupUserList,
  immediate: false,
  formConfig: {
    labelWidth: 120,
    schemas: userFormSchemas,
    autoSubmitOnEnter: true,
  },
  beforeFetch(info) {
    //info.orderBy = 'identity_type,a.auto_id'
    info.sortType = 'desc,asc'
    return { ...info, groupId: unref(groupId) }
  },
  maxHeight: 430,
  useSearchForm: true,
  bordered: true,
  actionColumn: {
    title: '操作',
    width: 150,
    dataIndex: 'action',
    fixed: undefined,
  },
})

const [registerModal] = useModalInner(async data => {
  groupId.value = data.record.groupId
  title.value = data.record.groupName + ' - 成员管理'
  await reload({
    searchInfo: {
      groupId: unref(groupId),
    },
  })
})

function handleCancleEdit(record) {
  currentEditKeyRef.value = ''
  record.onEdit?.(false, false)
}

async function handleSave(record: EditRecordRow) {
  const { userId, groupId, identityType, userState:state } = record
  const params = { userId, groupId, identityType,state}

  updateState(params).then(async res => {
    const { code, message } = res
    if (code === 200) {
      Modal.success({
        title: '提示',
        icon: createVNode(CheckCircleOutlined),
        content: '操作成功!' || message,
        okText: '确认',
        closable: true,
      })
      const pass = await record.onEdit?.(false, true)
      if (pass) {
        currentEditKeyRef.value = ''
      }
      await reload({
        searchInfo: {
          groupId: unref(groupId),
        },
      })
    } else {
      Modal.error({
        title: '提示',
        icon: createVNode(CloseCircleFilled),
        content: `操作失败!${message}`,
        okText: '确认',
        closable: true,
      })
    }
  })
}

function createActions(record: EditRecordRow, column): ActionItem[] {
  if (!record.editable) {
    return [
      {
        icon: 'carbon:task-view',
        label: '详情',
        type: 'default',
        onClick: handleDetails.bind(null, record),
      },
      {
        icon: 'fa6-solid:pen-to-square',
        type: 'primary',
        label: '编辑',
        disabled: currentEditKeyRef.value ? currentEditKeyRef.value !== record.key : false,
        onClick: handleEdit.bind(null, record),
      },
      {
        icon: 'fluent:delete-20-filled',
        label: '删除',
        type: 'primary',
        danger: true,
        onClick: handleDelete.bind(null, record),
      },
    ]
  }

  return [
    {
      label: '保存',
      onClick: handleSave.bind(null, record, column),
    },
    {
      label: '取消',
      popConfirm: {
        title: '是否取消编辑',
        confirm: handleCancleEdit.bind(null, record, column),
      },
    },
  ]
}

function handleEdit(record: EditRecordRow) {
  currentEditKeyRef.value = record.key
  record.onEdit?.(true)
}

function handleDetails(record) {
  openDetailsModel(true, {
    record: record,
    isUpdate: true,
    disabled: true,
  })
}

function handleDelete(record) {
  const { groupId, userId,userName } = record
  Modal.confirm({
    title: '信息',
    icon: createVNode(ExclamationCircleOutlined),
    content: `确定要删除${userName}?`,
    okText: '确认',
    cancelText: '取消',
    async onOk() {
      try {
        return await new Promise((resolve, reject) => {
          deleteLine({groupId,userId}).then(res => {
            if (res.code === 200) {
              message.success('删除成功')
              reload({
                searchInfo: {
                  groupId: unref(groupId),
                },
              })
              resolve(res)
            } else {
              message.error(res.message)
              reject(res)
            }
          })
        })
      } catch (error) {
        return console.log(error, 'Oops errors!')
      }
    },
  })
}

function handleEnd({ record, column, value }) {
  record[column.dataIndex] = value
}
</script>

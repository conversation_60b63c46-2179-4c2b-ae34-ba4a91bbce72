import {map} from 'lodash-es';
import { BasicColumn, FormSchema } from '/@/components/Table';
import {list} from "@/api/interestGroupManage/groupLabel";
import {Tag} from "ant-design-vue";
import {useDictionary} from "@/store/modules/dictionary";
export const columns = (): BasicColumn[] => {
  return [
    {
      title: '主键',
      dataIndex: 'autoId',
      defaultHidden: true,
    },
    {
      title: '用户姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '昵称',
      dataIndex: 'nickName',
      width: 150,
    },
    {
      title: '工会名称',
      dataIndex: 'companyName',
      width: 150,
    },
    {
      title: '个人标签',
      dataIndex: 'labelIds',
      width: 150,
      customRender({record}){
        return (
            <div>
              {map(record?.labels || [], t => (
                  <div className={`inline-block p-1`}>
                    <Tag color={'blue'}>{t.labelName}</Tag>
                  </div>
              ))}
            </div>
        );
      },
    },
    {
      title: '兴趣小组',
      dataIndex: 'interestGroups',
      width: 150,
      customRender({record}){
        return (
            <div>
              {map(record?.interestGroups || [], t => (
                  <div className={`inline-block p-1`}>
                    <Tag color={'blue'}>{t.groupName}</Tag>
                  </div>
              ))}
            </div>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 120,
    },
  ];
};

export const formSchemas = (): FormSchema[] => {
  return [
    {
      field: 'userName',
      label: '用户姓名',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel:true,
    },
    {
      field: 'nickName',
      label: '昵称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel:true,
    },
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel:true,
    },
    {
      field: 'companyName',
      label: '工会名称',
      component: 'Input',
      colProps: { span: 6 },
      rulesMessageJoinLabel:true,
    },
    {
      field: 'searchLabelId',
      label: '个人标签',
      component: 'ApiSelect',
      colProps: { span: 6 },
      componentProps: ( ) => {
        return {
          placeholder: '请选择小组标签',
          api: list,
          resultField: 'data',
          params: {
            pageSize:0,
          },
          immediate: true,
          onChange: () => {
          },
          showSearch: true,
          filterOption: (input: string, option: any) => {
            return option.labelName.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          },
          fieldNames: { label: 'labelName', value: 'autoId' },
        };
      },
    },
  ];
};


export const userModalForm = (): FormSchema[] => {
  const dictionary = useDictionary();
  return [
    {
      field: 'userName',
      label: '用户姓名',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    {
      field: 'userType',
      label: '成员身份',
      component: 'Select',
      colProps: { span: 12 },
      componentProps: {
        options: dictionary.getDictionaryOpt.get('teamType'),
      },
    },
    {
      field: 'officeName',
      label: '职务',
      component: 'Select',
      colProps: { span: 12 },
    },
    {
      field: 'content',
      label: '个人简介',
      component: 'Select',
      colProps: { span: 12 },
    },
    // {
    //   field: 'nickName',
    //   label: '昵称',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    // {
    //   field: 'gender',
    //   label: '性别',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    // {
    //   field: 'age',
    //   label: '年龄',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    {
      field: 'phone',
      label: '联系电话',
      colProps: { span: 12 },
      component: 'ShowSpan',
    },
    // {
    //   field: 'areaName',
    //   label: '所属区域',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    // {
    //   field: 'companyName',
    //   label: '工会名称',
    //   colProps: { span: 12 },
    //   component: 'ShowSpan',
    // },
    // {
    //   field: 'labels',
    //   label: '个人标签',
    //   component: 'ShowSpan',
    //   colProps:{span:12},
    //   render({values}){
    //     return (
    //         <div>
    //           {map(values?.labels || [], t => (
    //               <div className={`inline-block p-1`}>
    //                 <Tag color={'blue'}>{t.labelName}</Tag>
    //               </div>
    //           ))}
    //         </div>
    //     );
    //   },
    // },
    {
      field: 'interestGroups',
      label: '加入小组',
      component: 'ShowSpan',
      render({values}){
        return (
            <div>
              {map(values?.interestGroups || [], t => (
                  <div className={`inline-block p-1`}>
                    <Tag color={'blue'}>{t.groupName}</Tag>
                  </div>
              ))}
            </div>
        );
      },
    },
  ];
};


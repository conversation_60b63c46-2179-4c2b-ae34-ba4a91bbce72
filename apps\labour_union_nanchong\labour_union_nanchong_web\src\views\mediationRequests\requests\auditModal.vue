<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts">
import { computed, defineComponent, ref, unref } from 'vue'
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { auditModalForm } from './data'

export default defineComponent({
  name: 'AuditModal',
  components: { BasicModal, BasicForm },
  emits: ['success', 'register', 'cancel'],
  setup(_, { emit }) {
    const educationInfoId = ref('')

    const autoId = ref('')

    const userName = ref('')

    const record = ref()

    const title = computed(() => {
      return `审核--${unref(record)?.userName || ''}`
    })

    const [registerModal, { setModalProps, closeModal }] = useModalInner(async data => {
      await resetFields()
      setModalProps({
        confirmLoading: false,
      })
      autoId.value = data.record.autoId
      userName.value = data.record.userName
      record.value = data.record
    })

    const [registerForm, { resetFields, validate }] = useForm({
      labelWidth: 120,
      schemas: auditModalForm,
      showActionButtonGroup: false,
    })

    async function handleSubmit() {
      try {
        const values = await validate()
        values.lawyerCertificate = values.lawyerCertificate?.toString()
        values.resumePath = values.resumePath?.toString()
        values.applyDescription = values.applyDescription?.toString()
        setModalProps({ confirmLoading: true })
        emit('success', { ...values, autoId: autoId.value })
        closeModal()
      } finally {
        setModalProps({ confirmLoading: false })
      }
    }

    return {
      registerModal,
      handleSubmit,
      title,
      registerForm,
    }
  },
})
</script>

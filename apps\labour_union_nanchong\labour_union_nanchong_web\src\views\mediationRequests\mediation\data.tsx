import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '/@/api/sys/upload'
import { validateIdNum, validatePhone } from '@monorepo-yysz/utils';
import { getUserListHasCompanyId } from '/@/api/educationAid/hardWorkerAudit'
// import { findVoList } from '/@/api/consultation/lawyersType'
import { nextTick } from 'vue'
import {useUserStore} from "/@/store/modules/user";
import { RadioGroupChildOption } from 'ant-design-vue/es/radio/Group';

const dictionary = useDictionary()
const userStore = useUserStore()


export const columns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    defaultHidden: true,
  },
  {
    title: '申请人名称',
    dataIndex: 'applicantName',
    width: 100,
  },
  {
    title: '联系方式',
    dataIndex: 'applicantPhone',
    width: 100,
  },
  {
    title: '争议标题',
    dataIndex: 'reqTitle',
    width: 100,
  },

  {
    title: '争议类别',
    dataIndex: 'category',
    width: 100,
    customRender: ({ text }) => {
      return <span>{dictionary.getDictionaryMap.get(`expertiseArea_${text}`)?.dictName}</span>
    },
  },
  {
    title: '调解员名称',
    dataIndex: 'mediatorName',
    width: 100,
  },
  {
    title: '审核状态',
    dataIndex: 'mediationStatus',
    width: 100,
    customRender: ({ text }) => {
      return (
        <span
          class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
        >{dictionary.getDictionaryMap.get(`groupStudyStatus_${text}`)?.dictName}</span>
      );
    },
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    width: 100,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
]

export const formSchemas: FormSchema[] = [

  {
    field: 'applicantName',
    component: 'Input',
    label: '申请人姓名',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入申请人姓名',
    },
  },

  {
    field: 'applicantPhone',
    component: 'Input',
    label: '联系方式',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入联系方式',
    },
  },
  {
    field: 'mediatorName',
    component: 'Input',
    label: '调解员名称',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入调解员名称',
    },
  },
  {
    field: 'category',
    component: 'Select',
    label: '争议类型',
    colProps: { span: 6 },
    itemProps: {
      autoLink: true,
    },
    componentProps: function () {
      return {
        fieldNames: { label: 'dictName', value: 'dictCode' },
        options: dictionary.getDictionaryOBJMap.get('expertiseArea'),
      }
    },
   
  },
]

export const modalForm = (  disabled: boolean, isUpdate: boolean,): FormSchema[] =>{
  return[
    {
      field: 'applicantName',
      component: 'Input',
      label: '申请人姓名',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'applicantPhone',
      component: 'Input',
      label: '申请人电话',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'applicantCard',
      component: 'Input',
      label: '申请人身份证',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'mediationManner',
      component: 'Select',
      label: '调解方式',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps:{
        options: dictionary.getDictionaryOpt.get('onlineType'),
      }
    },
    {
      field: 'reqTitle',
      label: '争议标题',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
    },
    {
      field: 'category',
      label: '争议类型',
      required: true,
      component: 'Select',
      colProps: { span: 12 },
      componentProps:{
        options: dictionary.getDictionaryOpt.get('expertiseArea'),
      }
    },
    {
      field: 'reqContent',
      label: '争议事项',
      colProps: { span: 12 },
      component: 'Input',
      rules: [{ required: true, validator: validateIdNum, trigger: ['change', 'blur'] }],
    },
    {
      field: 'createTime',
      label: '申请时间',
      colProps: { span: 12 },
      component: 'Input',
    },

    {
      field: 'mediationStatus',
      label: '审核状态',
      colProps: { span: 12 },
      component: 'RadioGroup',
      // required: true,
      componentProps: {
        options: dictionary.getDictionaryOpt.get('groupStudyStatus') as RadioGroupChildOption[],
      },
    },
    {
      field: 'mediatorName',
      component: 'Input',
      label: '调解员名称',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
    },
    {
      field: 'mediationTime',
      component: 'Input',
      label: '调解时间',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
    },
    {
      field: 'mediationRemark',
      component: 'Input',
      label: '调解备注',
      required: true,
      colProps: { span: 12 },
      itemProps: {
        autoLink: true,
      },
    },
    
  ]
}

export const auditModalForm: FormSchema[] = [
  {
    field: 'operateType',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'pass',
        },
        {
          label: '拒绝',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'remark',
    label: '审核意见',
    required: function (Recordable) {
      if (Recordable.model.auditstatus == 'refuse') {
        return true
      } else {
        return false
      }
    },
    component: 'InputTextArea',
  },
]


//选择用户弹框筛选条件
export const UserFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//筛选用户列表
export const Usercolumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },

    {
      title: '电话',
      dataIndex: 'phone',
    },
  ]
}

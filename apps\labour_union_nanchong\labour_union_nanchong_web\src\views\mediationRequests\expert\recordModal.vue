<template>
  <BasicModal @register="registerModal" :title="title" v-bind="$attrs" @ok="handleSubmit">
    <BasicForm @register="registerForm" :class="disabledClass">
      <template #button="{ model, field }">
        <a-input
            type="primary"
            @click="choiceUser(model, field)"
            :disabled="disabled"
            v-model:value="model[field]"
            placeholder="请选择人员"
            autocomplete="off"
        ></a-input>
      </template>
      <template #button1="{ model, field }">
        <a-input
            type="primary"
            @click="choiceCadre(model, field)"
            :disabled="disabled"
            v-model:value="model[field]"
            placeholder="请选择人员"
            autocomplete="off"
        ></a-input>
      </template>
      <template #button2="{ model, field }">
        <a-input
            type="primary"
            @click="choiceWorker(model, field)"
            :disabled="disabled"
            v-model:value="model[field]"
            placeholder="请选择人员"
            autocomplete="off"
        ></a-input>
      </template>
    </BasicForm>
  </BasicModal>
  <!--用户列表-->
  <UserListModal
      @register="registerUserListModal"
      :canFullscreen="false"
      width="60%"
      @success="handleSuccess"
  />
  <CadreListModal
      @register="registerCadreListModal"
      :canFullscreen="false"
      width="60%"
      @success="handleCadreSuccess"
  />
  <WorkerModel
      @register="registerWorkerListModal"
      :canFullscreen="false"
      width="60%"
      @success="handleWorkerSuccess"
  />
</template>

<script lang="ts" setup>
import {computed, ref, unref, watch} from 'vue'
import {BasicModal, useModal, useModalInner} from '/@/components/Modal'
import {BasicForm, useForm} from '/@/components/Form'
import {modalForm} from './record'
import UserListModal from './userListModal.vue'
import CadreListModal from './cadreListModal.vue'
import WorkerModel from './workerModel.vue'
import { useDictionary } from '/@/store/modules/dictionary'
const emit = defineEmits(['success', 'register'])

const model = ref<Recordable>()

const dictionary = useDictionary()

const field = ref('')

const isUpdate = ref(true)

const disabled = ref(false)

const record = ref<Recordable>()

const autoId = ref('')

const userId = ref('')
const name = ref('')
const companyId = ref('')

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const title = computed(() => {
  return unref(disabled)
      ? `${unref(record)?.lawyerName || ''}--律师详情`
      : unref(isUpdate)
          ? `编辑律师--${unref(record)?.lawyerName || ''}--信息`
          : '新增律师'
})

// 人员列表
const [registerUserListModal, {openModal: openUserListModal, closeModal}] = useModal()
// 工会干部列表
const [registerCadreListModal, {openModal: openCadreListModal, closeModal: closeCadreListModal}] = useModal()
//劳模工匠
const [registerWorkerListModal, {openModal: openWorkerListModal, closeModal: closeWorkerListModal}] = useModal()

const [registerModal, {setModalProps}] = useModalInner(async data => {
  await resetFields()

  isUpdate.value = !!data?.isUpdate

  disabled.value = !!data?.disabled

  record.value = data.record

  autoId.value = data.record?.autoId

  if (unref(isUpdate)) {
    setFieldsValue({
      ...data.record,
      domainType : data.record.domainType.split(','),
    })
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  })

  setProps({disabled: unref(disabled)})
})

const form = computed(() => {
  return modalForm(unref(disabled), unref(isUpdate))
})

const [registerForm, {setFieldsValue, resetFields, validate, setProps}] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
})

async function handleSubmit() {
  try {
    const values = await validate()
    values.domainType = values.domainType?.join(',')
    setModalProps({confirmLoading: true})
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
        autoId: isUpdate.value ? autoId.value : undefined,
        name: unref(name),
      },
    })
  } finally {
    setModalProps({confirmLoading: false})
  }
}

// 用户列表
function choiceUser(m, f) {
  openUserListModal(true)
  model.value = m
  field.value = f
}

// 工会干部列表
function choiceCadre(m, f) {
  openCadreListModal(true)
  model.value = m
  field.value = f
}

// 工会干部列表
function choiceWorker(m, f) {
  openWorkerListModal(true)
  model.value = m
  field.value = f
}


// 确定按钮
function handleSuccess(userNameMy,phone,cadreIdentity,gender) {
  name.value = userNameMy
  setFieldsValue({phone:phone,idCard:cadreIdentity,sex:gender})
  closeModal()
}

// 工会干部列表
function handleCadreSuccess(userNameMy,phone,cadreIdentity,gender,deptName) {
  name.value = userNameMy
  setFieldsValue({phone:phone,idCard:cadreIdentity,sex:gender == '1' ? 'male' : 'female',deptName:deptName})
  closeCadreListModal()
}

// 劳模工匠列表
function handleWorkerSuccess(userNameMy,phone,cadreIdentity,gender) {
  console.log(userNameMy,phone,cadreIdentity,gender)
  name.value = userNameMy
  console.log(userNameMy)
  
  setFieldsValue({phone:phone,idCard:cadreIdentity,sex:gender})
  closeWorkerListModal()
}
// 监听
watch(name, () => {
  if (model.value) {
    model.value[unref(field)] = unref(name)
  }
  console.log(unref(name))
})

// return {
//   registerModal,
//   handleSubmit,
//   title,
//   registerForm,
//   disabledClass,
//
// }
// },
// })
</script>

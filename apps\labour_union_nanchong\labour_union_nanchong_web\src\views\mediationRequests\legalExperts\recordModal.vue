<template>
  <BasicModal
    @register="registerModal"
    :title="title"
    v-bind="$attrs"
    @ok="handleSubmit"
  >
    <BasicForm
      @register="registerForm"
      :class="disabledClass"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import {BasicForm, useForm} from '/@/components/Form'
import { modalForm } from './record';

const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);

const disabled = ref(false);

const record = ref<Recordable>();

const autoId = ref('');

const userId = ref('');
const userName = ref('');
const companyId = ref('');

const disabledClass = computed(() => {
  return unref(disabled) ? 'back-transparent' : ''
})

const title = computed(() => {
  return unref(disabled)
    ? `${unref(record)?.lawyerName || ''}--律师详情`
    : unref(isUpdate)
      ? `编辑律师--${unref(record)?.lawyerName || ''}--信息`
      : '新增律师';
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();

  isUpdate.value = !!data?.isUpdate;

  disabled.value = !!data?.disabled;

  record.value = data.record;

  autoId.value = data.record?.autoId;

  if (unref(isUpdate)) {
    const { otherFilePath, aidApplyFilePath } = data.record;
    setFieldsValue({
      ...data.record,
      aidApplyFilePath: aidApplyFilePath ? aidApplyFilePath.split(',') : [],
      otherFilePath: otherFilePath ? otherFilePath.split(',') : [],
    });
  }
  setModalProps({
    confirmLoading: false,
    showOkBtn: !unref(disabled),
  });

  setProps({ disabled: unref(disabled) });
});

const form = computed(() => {
  return modalForm(unref(disabled), unref(isUpdate));
});

const [registerForm, { setFieldsValue, resetFields, validate, setProps }] = useForm({
  labelWidth: 120,
  schemas: form,
  showActionButtonGroup: false,
});

async function handleSubmit() {
  try {
    const values = await validate();
    values.lawyerCertificate = values.lawyerCertificate?.toString();
    values.resumePath = values.resumePath?.toString();
    values.applyDescription = values.applyDescription?.toString();
    setModalProps({ confirmLoading: true });
    emit('success', {
      isUpdate: unref(isUpdate),
      values: {
        ...record.value,
        ...values,
        autoId: isUpdate.value ? autoId.value : undefined,
        userId: unref(userId),
        userName: unref(userName),
        companyId: unref(companyId),
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}

</script>

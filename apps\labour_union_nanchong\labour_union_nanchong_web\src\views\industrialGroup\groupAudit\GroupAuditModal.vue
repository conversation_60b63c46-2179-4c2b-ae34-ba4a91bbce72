<template>
  <BasicModal
    @register="registerModal"
    v-bind="$attrs"
    :title="title"
    :destroyOnClose="true"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #answerRecords="{ model, field }">
        <div v-if="model[field] && model[field].length > 0">
          <Divider>报名信息</Divider>
          <div v-for="(item, index) in model[field]">
            <Title :level="5">{{index+1}}、{{ item.title }}</Title>
            <Paragraph :content="item.content"
            />
          </div>
        </div>
      </template>
    </BasicForm>
    <BasicForm
      v-if="isAudit"
      @register="registerAudit"
    >
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, unref, computed } from 'vue';
import { useModalInner, BasicModal } from '@/components/Modal';
import { useForm, BasicForm } from '@/components/Form';
import { groupAuditForm, modalForm } from '@/views/industrialGroup/groupAudit/data';
import {Divider, Typography} from "ant-design-vue";

const Title = Typography.Title;
const Paragraph = Typography.Paragraph;

const emit = defineEmits(['register', 'success']);

const record = ref<Recordable>();
const isAudit = ref(false);
const disabled = ref(false);
const auditRecord = ref<Recordable>();
const disabledClass = computed(() => {
  return unref(isAudit) ? 'back-transparent' : '';
});

const title = computed(() => {
  if (unref(isAudit)) {
    return `${unref(auditRecord)?.nickName} - 审核`;
  }
  return `${unref(auditRecord)?.nickName || unref(auditRecord)?.userName} - 详情`;
});

const formItem = computed(() => {
  return modalForm(unref(auditRecord)?.sourceType);
});

const [registerForm, { resetFields, setProps, setFieldsValue }] = useForm({
  labelWidth: 100,
  schemas: formItem,
  showActionButtonGroup: false,
});

const [registerAudit, { resetFields: resetAuditFields, validate }] = useForm({
  labelWidth: 100,
  schemas: groupAuditForm(),
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps }] = useModalInner(async data => {
  await resetFields();
  record.value = data.record;
  auditRecord.value = data.auditRecord;
  disabled.value = !!data.disabled;
  isAudit.value = !!data.isAudit;
  await setFieldsValue({ ...data.record });
  setProps({ disabled: !unref(isAudit) });
  setModalProps({ confirmLoading: false, showOkBtn: unref(isAudit) });
});
async function handleSubmit() {
  try {
    setModalProps({ confirmLoading: true });

    const values = await validate();
    emit('success', {
      values: {
       autoId:unref(auditRecord).autoId,
        ...values,
      },
    });
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>

import { h5Http } from '/@/utils/http/axios'

import { BasicResponse } from '@monorepo-yysz/types';

enum recordUrl {
  findList = '/findVoList',
  delete = '/record/delete',
  saveOrUpdate = '/record/saveOrUpdate',
  applyAudit = '/applyAudit',
  details = '/record/getDetails',
  saveOrUpdateByDTO = '/saveOrUpdateByDTO',
  getByEntity = '/getByEntity',
  logicDelById = '/logicDelById',
}

function getApi(url?: string) {
  if (!url) {
    return '/mediationRequestsPengAn'
  }
  return '/mediationRequestsPengAn' + url
}

/*列表管理*/
export const findList = params => {
  return h5Http.get<BasicResponse>(
    {
      url: getApi(recordUrl.findList),
      params,
    },
    { isTransformResponse: false },
  )
}

/*详情*/
export const getByEntity = params => {
    return h5Http.get<BasicResponse>(
        {
            url: getApi(recordUrl.getByEntity),
            params,
        },
        { isTransformResponse: false },
    )
}

/*新增/编辑管理*/
export const saveOrUpdateByDTO = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.saveOrUpdateByDTO),
      params,
    },
    { isTransformResponse: false },
  )
}
/*批量审核*/
export const applyAudit = params => {
  return h5Http.post<BasicResponse>(
    {
      url: getApi(recordUrl.applyAudit),
      params,
    },
    { isTransformResponse: false },
  )
}

/*删除管理*/
export const logicDelById = params => {
  return h5Http.delete<BasicResponse>(
    {url: getApi(recordUrl.logicDelById)+`?autoId=${params}`,
    params,
  },
    
    { isTransformResponse: false },
  )
}

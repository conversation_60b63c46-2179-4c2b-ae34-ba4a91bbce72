import { BasicColumn, FormSchema } from '/@/components/Table'
import { useDictionary } from '/@/store/modules/dictionary'
import { uploadApi } from '/@/api/sys/upload'

import {useUserStore} from "/@/store/modules/user";
import { newsExtractKeywords } from '@/api/news';
import { message } from 'ant-design-vue';
import { map } from 'lodash-es';
const dictionary = useDictionary()
const userStore = useUserStore()


export const columns: BasicColumn[] = [
  {
    title: '主键',
    dataIndex: 'autoId',
    defaultHidden: true,
  },
  {
    title: '企业名称',
    dataIndex: 'policiesCompanyName',
    width: 100,
  },
  {
    title: '政策编号',
    dataIndex: 'policyCode',
    width: 100,
  },
  {
    title: '政策名称',
    dataIndex: 'policyName',
    width: 100,
  },
  {
    title: '政策类型',
    dataIndex: 'policyType',
    width: 100,
    customRender: ({ text }) => {
        return dictionary.getDictionaryMap.get(`policyType_${text}`)?.dictName || '';
      },
  },

  {
    title: '发布状态',
    dataIndex: 'publisherState',
    width: 100,
    customRender: ({ text }) => {
      return (
        <span
          class={`${text === 'pass' ? 'text-green-500' : text === 'refuse' ? 'text-red-500' : ''}`}
        >{dictionary.getDictionaryMap.get(`policiesPublishStatus_${text}`)?.dictName}</span>
      );
    },
  },
  {
    title: '政策发布时间',
    dataIndex: 'policyTime',
    width: 100,
    format: 'date|YYYY-MM-DD',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
    format: 'date|YYYY-MM-DD HH:mm:ss',
  },
]

export const formSchemas: FormSchema[] = [

  {
    field: 'policyName',
    component: 'Input',
    label: '政策名称',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入政策名称',
    },
  },

  {
    field: 'policiesCompanyName',
    component: 'Input',
    label: '政策单位',
    colProps: {
      span: 5,
    },
    componentProps: {
      autocomplete: 'off',
      placeholder: '请输入政策单位',
    },
  },
]

export const modalForm = (disabled: boolean, isUpdate: boolean): FormSchema[] => {
  return [
    {
      field: 'policiesCompanyName',
      component: 'Input',
      label: '企业名称',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'policyCode',
      component: 'Input',
      label: '政策编号',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'policyName',
      component: 'Input',
      label: '政策名称',
      colProps: {
        span: 12,
      },
      required: true,
    },
    {
      field: 'policyType',
      component: 'Select',
      label: '政策类型',
      colProps: {
        span: 12,
      },
      required: true,
      componentProps:{
        options: dictionary.getDictionaryOpt.get('policyType'),
      }
    },
    {
      field: 'policyTime',
      label: '政策发布时间',
      required: true,
      component: 'DatePicker',
      colProps: { span: 12 },
      componentProps: {
        valueFormat: `YYYY-MM-DD`,
        format: `YYYY-MM-DD`,
      },
    },
    {
      field: 'attachment',
      label: '政策文件',
      colProps: { span: 12 },
      component: 'Upload',
      // required: true,
      componentProps: {
        api: uploadApi,
        uploadParams: {
          operateType: 132,
        },
        maxNumber: 1,
        maxSize: 10,
      },
    },
    {
      field: 'content',
      label: '政策内容',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
      componentProps:{
        maxlength:400,
        showCount: true,
        placeholder:"请输入政策内容"
      }
    },
    {
      field: 'keywords',
      label: '关键词',
      colProps: { span: 24 },
      component: 'CheckboxGroup',
      componentProps: ({ formModel }) => {
        // 只用 keywordOptions，不再回退 keywords
        const keywordOptions = formModel.keywordOptions || [];
        console.log('重新生成选项，keywordOptions:', keywordOptions); // 调试信息
        return {
          options: keywordOptions.map(item => ({
            label: item,
            value: item,
          })),
        }
      }
    },
    {
      field: 'keywordOptions',
      component: 'Input',
      label: '',
      show: false, // 隐藏这个字段
    },
  ]
}

export const auditModalForm: FormSchema[] = [
  {
    field: 'operateType',
    label: '审核状态',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        {
          label: '通过',
          value: 'pass',
        },
        {
          label: '拒绝',
          value: 'refuse',
        },
      ],
    },
  },
  {
    field: 'remark',
    label: '审核意见',
    required: function (Recordable) {
      if (Recordable.model.auditstatus == 'refuse') {
        return true
      } else {
        return false
      }
    },
    component: 'InputTextArea',
  },
]


//选择用户弹框筛选条件
export const UserFormSchemas = (): FormSchema[] => {
  return [
    {
      field: 'nickname',
      label: '姓名',
      colProps: { span: 12 },
      component: 'Input',
      rulesMessageJoinLabel: true,
    },
  ]
}

//筛选用户列表
export const Usercolumns = (): BasicColumn[] => {
  return [
    {
      title: '姓名',
      dataIndex: 'nickname',
    },

    {
      title: '电话',
      dataIndex: 'phone',
    },
  ]
}

/**
 * 提取政策内容关键词
 * @param content 政策内容
 * @returns 逗号分隔的关键词字符串
 */
export async function extractKeywordsForPolicy(content: string): Promise<Array<string>> {
  if (!content || !content.trim()) {
    message.warning('请先输入政策内容');
    return [];
  }
  try {
    const res = await newsExtractKeywords({
      content,
      returnKeywordsNumber: 20,
    });
    if (res?.data && Array.isArray(res.data) && res.data.length > 0) {
      message.success(`成功提取${res.data.length}个关键词`);
      return res.data;
    } else {
      message.warning('未提取到关键词');
      return [];;
    }
  } catch (e) {
    message.error('提取失败，请稍后重试');
    return [];;
  }
}
